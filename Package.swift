// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "VideoAdapterCore",
    defaultLocalization: "en",
    platforms: [
        .iOS(.v16)
    ],
    products: [
        .library(name: "VideoAdapterCore", targets: ["VideoAdapterCore"])
    ],
    targets: [
        .target(
            name: "VideoAdapterCore",
            resources: [
                .process("Resources")
            ]
        ),
        .testTarget(
            name: "VideoAdapterCoreTests",
            dependencies: ["VideoAdapterCore"]
        )
    ]
)

