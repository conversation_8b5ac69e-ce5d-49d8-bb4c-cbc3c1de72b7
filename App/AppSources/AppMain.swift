import SwiftUI
import VideoAdapterCore
import PhotosUI

@main
struct VideoAdapterApp: App {
    var body: some Scene { WindowGroup { TabRootView() } }
}

struct TabRootView: View {
    var body: some View {
        TabView {
            MainView()
                .tabItem { Label("单个", systemImage: "film") }
            BatchView()
                .tabItem { Label("批量", systemImage: "square.stack.3d.down.right") }
            SettingsView()
                .tabItem { Label("设置", systemImage: "gear") }
        }
    }
}

struct MainView: View {
    @StateObject private var vm = CompressionViewModel()
    @State private var pickedURL: URL?
    @State private var showingPicker = false
    @State private var showShare = false

    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                Picker("预设", selection: Binding(get: { vm.selectedPreset?.id ?? "" }, set: { id in
                    vm.selectedPreset = vm.presets.first(where: { $0.id == id })
                })) {
                    ForEach(vm.presets, id: \.id) { p in Text(p.name).tag(p.id) }
                }.pickerStyle(.menu)

                Button("选择视频(文件)") { showingPicker = true }
                if let url = pickedURL {
                    Text(url.lastPathComponent).font(.caption).lineLimit(1)
                    if vm.estimatedOutputMB > 0 { Text(String(format: "预计输出约 %.1f MB", vm.estimatedOutputMB)).font(.footnote) }
                    HStack {
                        Button(vm.isProcessing ? "正在压缩…" : "开始压缩") { Task { await vm.compress(url: url) } }.disabled(vm.isProcessing)
                        Button("导出到文件App") { vm.saveToFiles() }.disabled(vm.lastOutputURL == nil)
                    }
                }

                if vm.isProcessing { ProgressView(value: vm.progress) }

                if let out = vm.lastOutputURL {
                    Text("已输出：\(out.lastPathComponent)")
                    HStack {
                        Button("保存到相册") { Task { await vm.saveToPhotos() } }
                        Button("分享…") { showShare = true }
                    }
                    .sheet(isPresented: $showShare) { ShareSheet(activityItems: [out]) }
                }

                if let err = vm.errorMessage { Text(err).foregroundColor(.red) }
                Spacer()
            }
            .padding()
            .navigationTitle("单个压缩")
            .onAppear { vm.loadPresets() }
            .fileImporter(isPresented: $showingPicker, allowedContentTypes: [.movie], allowsMultipleSelection: false) { result in
                switch result {
                case .success(let urls): pickedURL = urls.first; if let u = pickedURL { Task { await vm.inspect(url: u) } }
                case .failure: pickedURL = nil
                }
            }
        }
    }
}

struct BatchView: View {
    @StateObject private var vm = CompressionViewModel()
    @State private var showPicker = false
    @State private var photoSelection: [PhotosPickerItem] = []

    var body: some View {
        NavigationView {
            VStack(spacing: 12) {
                Picker("预设", selection: Binding(get: { vm.selectedPreset?.id ?? "" }, set: { id in
                    vm.selectedPreset = vm.presets.first(where: { $0.id == id })
                })) {
                    ForEach(vm.presets, id: \.id) { p in Text(p.name).tag(p.id) }
                }.pickerStyle(.menu)

                HStack {
                    Button("添加(文件)") { showPicker = true }
                    PhotosPicker("添加(相册)", selection: $photoSelection, matching: .videos)
                        .onChange(of: photoSelection) { newItems in
                            Task {
                                var urls: [URL] = []
                                for item in newItems { if let u = await PhotosPickerUtils.resolveToTempURL(item) { urls.append(u) } }
                                vm.addBatchItems(urls)
                            }
                        }
                }

                List(vm.batchItems, id: \.absoluteString) { url in
                    HStack {
                        Text(url.lastPathComponent).lineLimit(1)
                        Spacer()
                        ProgressView(value: vm.batchProgress[url] ?? 0)
                            .frame(width: 120)
                    }
                }

                HStack {
                    Button(vm.isBatchProcessing ? "处理中…" : "开始批量") { vm.compressBatch() }.disabled(vm.isBatchProcessing || vm.batchItems.isEmpty)
                    Button("保存全部到相册") { Task { await vm.saveAllToPhotos() } }.disabled(vm.batchItems.isEmpty)
                    Button("保存全部到文件") { vm.saveAllToFiles() }.disabled(vm.batchItems.isEmpty)
                    Button("取消") { vm.cancelAll() }.disabled(!vm.isBatchProcessing)
                    Button("重试失败项") { vm.retryFailed() }
                }

                if let err = vm.errorMessage { Text(err).foregroundColor(.red) }
                Spacer()
            }
            .padding()
            .navigationTitle("批量压缩")
            .onAppear { vm.loadPresets() }
            .fileImporter(isPresented: $showPicker, allowedContentTypes: [.movie], allowsMultipleSelection: true) { result in
                if case .success(let urls) = result { vm.addBatchItems(urls) }
            }
        }
    }
}

struct SettingsView: View {
    @StateObject private var vm = CompressionViewModel()
    @State private var showPaywall = false
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("导出")) {
                    Toggle("导出时移除元数据", isOn: $vm.removeMetadata)
                        .onChange(of: vm.removeMetadata) { _ in vm.saveSettings() }
                    Button("清理缓存") { vm.clearCache() }
                }
                Section(header: Text("专业版")) {
                    Button("解锁专业版（一次性）") { showPaywall = true }
                        .sheet(isPresented: $showPaywall) { PaywallView() }
                    Text("专业版解锁功能：批量压缩、高清预设等")
                        .font(.footnote).foregroundColor(.secondary)
                }
            }
            .navigationTitle("设置")
            .onAppear { vm.loadPresets() }
        }
    }
}

// 分享面板封装
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    func makeUIViewController(context: Context) -> UIActivityViewController { UIActivityViewController(activityItems: activityItems, applicationActivities: nil) }
    func updateUIViewController(_ vc: UIActivityViewController, context: Context) {}
}

