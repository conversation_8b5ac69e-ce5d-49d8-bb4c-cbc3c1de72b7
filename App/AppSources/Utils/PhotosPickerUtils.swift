import Foundation
import Photos
import PhotosUI

enum PhotosPickerUtils {
    /// 将 PhotosPickerItem 解析为可读写的临时文件URL（优先流式写入文件）
    static func resolveToTempURL(_ item: PhotosPickerItem) async -> URL? {
        // 1) 尝试直接拿到URL（部分来源可用）
        if let url = try? await item.loadTransferable(type: URL.self) { return url }
        // 2) 尝试以Data加载（大文件不优，但作为兜底）
        if let data = try? await item.loadTransferable(type: Data.self) {
            let tmp = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("picked_\(UUID().uuidString).mp4")
            do { try data.write(to: tmp); return tmp } catch { return nil }
        }
        // 3) 使用 PHAssetResourceManager 流式写入本地文件
        if let id = item.itemIdentifier {
            let assets = PHAsset.fetchAssets(withLocalIdentifiers: [id], options: nil)
            guard let asset = assets.firstObject else { return nil }
            let resources = PHAssetResource.assetResources(for: asset)
            guard let videoRes = resources.first(where: { $0.type == .video || $0.type == .pairedVideo || $0.type == .fullSizeVideo }) else { return nil }
            let dst = URL(fileURLWithPath: NSTemporaryDirectory()).appendingPathComponent("picked_\(UUID().uuidString).mov")
            let ok = await write(resource: videoRes, to: dst)
            return ok ? dst : nil
        }
        return nil
    }

    private static func write(resource: PHAssetResource, to url: URL) async -> Bool {
        await withCheckedContinuation { cont in
            let opts = PHAssetResourceRequestOptions()
            opts.isNetworkAccessAllowed = true
            PHAssetResourceManager.default().writeData(for: resource, toFile: url, options: opts) { error in
                cont.resume(returning: error == nil)
            }
        }
    }
}

