import Foundation
import SwiftUI
import VideoAdapterCore
import AVFoundation
import Photos
import PhotosUI

@MainActor
final class CompressionViewModel: ObservableObject {
    // 预设与设置
    @Published var presets: [Preset] = []
    @Published var selectedPreset: Preset?
    @Published var removeMetadata: Bool = true

    // 单文件处理
    @Published var isProcessing: Bool = false
    @Published var progress: Double = 0
    @Published var lastOutputURL: URL?
    @Published var estimatedOutputMB: Double = 0

    // 批量处理
    @Published var batchItems: [URL] = []
    @Published var batchProgress: [URL: Double] = [:]
    @Published var batchOutputs: [URL: URL] = [:]
    @Published var isBatchProcessing: Bool = false
    private var batchTask: Task<Void, Never>?
    private var failedItems: Set<URL> = []

    // 错误提示
    @Published var errorMessage: String?

    private let presetProvider = try? JSONPresetProvider()
    private let engine = BasicCompressionEngine()
    private let saver = PhotoLibrarySaver()
    private let fileExporter = FileExporter()

    // MARK: - 预设 & 设置
    func loadPresets() {
        do { presets = try presetProvider?.allPresets() ?? []
            if selectedPreset == nil { selectedPreset = presets.first }
            removeMetadata = UserDefaults.standard.bool(forKey: "removeMetadata")
        } catch { presets = [] }
    }
    func saveSettings() { UserDefaults.standard.set(removeMetadata, forKey: "removeMetadata") }

    // MARK: - 单文件
    func inspect(url: URL) async {
        guard let preset = selectedPreset else { estimatedOutputMB = 0; return }
        let asset = AVAsset(url: url)
        do { let dur = try await asset.load(.duration); let sec = CMTimeGetSeconds(dur)
            let bytes = Double(preset.bitrate) * 1000.0 * max(1.0, sec) / 8.0
            estimatedOutputMB = bytes / 1024.0 / 1024.0
        } catch { estimatedOutputMB = 0 }
    }

    func compress(url: URL) async {
        guard let preset = selectedPreset else { return }
        let free = DiskSpaceChecker.freeDiskSpaceInBytes()
        let need = Int64(estimatedOutputMB * 1024 * 1024 * 12 / 10)
        if free < need { errorMessage = "剩余空间不足，请清理磁盘后重试。"; return }
        isProcessing = true; progress = 0; errorMessage = nil
        let asset = MediaAsset(id: UUID().uuidString, url: url, duration: 0, dimension: .zero, fileSize: 0)
        do {
            let out = try await engine.compress(asset: asset, preset: preset) { [weak self] p in
                Task { @MainActor in self?.progress = p }
            }
            lastOutputURL = out
            isProcessing = false
        } catch {
            errorMessage = String(describing: error)
            isProcessing = false
        }
    }

    func saveToPhotos() async {
        guard let url = lastOutputURL else { return }
        do { try await saver.saveVideo(at: url) }
        catch PhotoSaveError.notAuthorized { errorMessage = "无相册权限，请到设置中开启‘照片’访问权限。" }
        catch { errorMessage = "保存失败：\(error.localizedDescription)" }
    }

    func saveToFiles() {
        guard let url = lastOutputURL else { return }
        _ = try? fileExporter.moveToDocuments(tempURL: url)
    }

    // MARK: - 批量
    func addBatchItems(_ urls: [URL]) { for u in urls where !batchItems.contains(u) { batchItems.append(u) } }

    func compressBatch() {
        guard let preset = selectedPreset else { return }
        isBatchProcessing = true; errorMessage = nil; batchProgress.removeAll(); batchOutputs.removeAll(); failedItems.removeAll()
        batchTask = Task { [weak self] in
            guard let self = self else { return }
            for url in self.batchItems {
                if Task.isCancelled { break }
                await self.inspect(url: url)
                let free = DiskSpaceChecker.freeDiskSpaceInBytes()
                let need = Int64(self.estimatedOutputMB * 1024 * 1024 * 12 / 10)
                if free < need { await self.markFailed(url); continue }
                let a = MediaAsset(id: UUID().uuidString, url: url, duration: 0, dimension: .zero, fileSize: 0)
                do {
                    let out = try await self.engine.compress(asset: a, preset: preset) { [weak self] p in
                        Task { @MainActor in self?.batchProgress[url] = p }
                    }
                    await self.markSucceeded(url, out: out)
                } catch {
                    await self.markFailed(url)
                }
            }
            await MainActor.run { self.isBatchProcessing = false }
        }
    }

    private func markSucceeded(_ url: URL, out: URL) async {
        await MainActor.run { batchOutputs[url] = out; batchProgress[url] = 1.0 }
    }
    private func markFailed(_ url: URL) async {
        await MainActor.run { failedItems.insert(url); batchProgress[url] = 0 }
    }

    func cancelAll() { batchTask?.cancel(); isBatchProcessing = false }

    func retryFailed() { let failed = Array(failedItems); failedItems.removeAll(); addBatchItems(failed); compressBatch() }

    func saveAllToPhotos() async { for (_, out) in batchOutputs { try? await saver.saveVideo(at: out) } }
    func saveAllToFiles() { for (_, out) in batchOutputs { _ = try? fileExporter.moveToDocuments(tempURL: out) } }

    func clearCache() { _ = CacheManager.cleanTemp() }
}

