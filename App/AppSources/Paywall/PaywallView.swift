import SwiftUI
import VideoAdapterCore

struct PaywallView: View {
    @State private var processing = false
    @StateObject private var store = StoreKitStore(productId: "pro_unlock_once") // TODO: 用真实产品ID

    var body: some View {
        VStack(spacing: 16) {
            Text("解锁专业版")
                .font(.title2)
            Text("解锁批量压缩、高清预设等高级功能")
                .multilineTextAlignment(.center)

            Button(processing ? "处理中…" : "一次性解锁") {
                Task { processing = true; try? await store.purchasePro(); processing = false }
            }
            .buttonStyle(.borderedProminent)

            Button("恢复购买") { Task { try? await store.restore() } }
                .font(.footnote)
                .foregroundColor(.secondary)

            if store.isProUnlocked { Text("已解锁·感谢支持！").foregroundColor(.green) }
        }
        .padding()
    }
}

