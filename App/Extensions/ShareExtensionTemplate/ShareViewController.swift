// Share Extension 模板控制器（将于Xcode中创建目标后使用）
// 使用UIKit（SLComposeServiceViewController）示例，可改为SwiftUI Hosting

import UIKit
import Social
import MobileCoreServices
import UniformTypeIdentifiers
import VideoAdapterCore

class ShareViewController: SLComposeServiceViewController {
    private var urls: [URL] = []
    private let engine = BasicCompressionEngine()
    private let presetProvider = try? JSONPresetProvider()

    override func isContentValid() -> Bool { return true }

    override func didSelectPost() {
        // 选默认/最近预设：此处简化为第一个
        guard let preset = try? presetProvider?.allPresets().first else { self.cancel(); return }
        Task {
            var last: URL?
            for url in urls { _ = try? await engine.compress(asset: MediaAsset(id: UUID().uuidString, url: url, duration: 0, dimension: .zero, fileSize: 0), preset: preset!) { _ in } }
            // 简化：完成即结束扩展
            self.extensionContext?.completeRequest(returningItems: nil, completionHandler: nil)
        }
    }

    override func configurationItems() -> [Any]! { return [] }

    override func viewDidLoad() {
        super.viewDidLoad()
        extractURLs()
    }

    private func extractURLs() {
        guard let items = extensionContext?.inputItems as? [NSExtensionItem] else { return }
        for item in items {
            guard let providers = item.attachments else { continue }
            for p in providers {
                if p.hasItemConformingToTypeIdentifier(UTType.movie.identifier) {
                    p.loadItem(forTypeIdentifier: UTType.movie.identifier, options: nil) { (item, _) in
                        if let u = item as? URL { self.urls.append(u) }
                    }
                }
            }
        }
    }
}

