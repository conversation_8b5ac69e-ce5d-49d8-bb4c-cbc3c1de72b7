// 占位文件：Share Extension 目标创建后迁移为独立Target的入口
// 说明：在Xcode中创建 Share Extension（Action Extension 或 Share Extension）
// 并将此处逻辑迁移到 Extension 的 ViewController/SwiftUI入口中。

import Foundation
import UniformTypeIdentifiers
import VideoAdapterCore

public enum ShareExtensionBridge {
    /// 将扩展输入的URL数组转换为 MediaAsset
    public static func makeAssets(from urls: [URL]) -> [MediaAsset] {
        return urls.enumerated().map { (idx, url) in
            MediaAsset(id: "ext-\(idx)", url: url, duration: 0, dimension: .zero, fileSize: 0)
        }
    }
}

