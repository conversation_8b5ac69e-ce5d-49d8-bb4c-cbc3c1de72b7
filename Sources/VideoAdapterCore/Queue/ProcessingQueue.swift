import Foundation

public protocol ProcessingQueueHandling: Sendable {
    func enqueue(_ job: Job)
    func cancel(jobId: String)
    var onProgress: ((String, Double) -> Void)? { get set }
    var onStatusChanged: ((String, JobStatus) -> Void)? { get set }
}

public final class InMemoryProcessingQueue: ProcessingQueueHandling {
    private let lock = NSLock()
    private var jobs: [Job] = []
    public var onProgress: ((String, Double) -> Void)?
    public var onStatusChanged: ((String, JobStatus) -> Void)?
    public init() {}
    public func enqueue(_ job: Job) { lock.lock(); jobs.append(job); lock.unlock() }
    public func cancel(jobId: String) { onStatusChanged?(jobId, .cancelled) }
}

