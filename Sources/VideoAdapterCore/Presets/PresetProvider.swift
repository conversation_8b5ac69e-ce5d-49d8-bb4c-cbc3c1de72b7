import Foundation

public protocol PresetProviding: Sendable {
    func allPresets() throws -> [Preset]
    func presets(for platform: Platform) throws -> [Preset]
}

public final class JSONPresetProvider: PresetProviding {
    private let data: Data
    public init(bundle: Bundle = .module) throws {
        guard let url = bundle.url(forResource: "presets", withExtension: "json") else {
            throw NSError(domain: "Preset", code: 1, userInfo: [NSLocalizedDescriptionKey: "presets.json not found"])
        }
        self.data = try Data(contentsOf: url)
    }
    public func allPresets() throws -> [Preset] { try JSONDecoder().decode([Preset].self, from: data) }
    public func presets(for platform: Platform) throws -> [Preset] { try allPresets().filter { $0.platform == platform } }
}

