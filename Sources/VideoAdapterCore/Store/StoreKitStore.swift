import Foundation
import StoreKit

public final class StoreKitStore: ObservableObject, _StoreProvidingAlias {
    private let productId: String
    @Published public private(set) var isProUnlocked: Bool = false

    public init(productId: String) {
        self.productId = productId
        Task { await refreshEntitlements() }
    }

    @MainActor
    public func isEntitled(_ e: Entitlement) -> Bool { isProUnlocked }

    @MainActor
    public func purchasePro() async throws {
        let products = try await Product.products(for: [productId])
        guard let product = products.first else { throw NSError(domain: "IAP", code: -1, userInfo: [NSLocalizedDescriptionKey: "Product not found"]) }
        let result = try await product.purchase()
        switch result {
        case .success(let verification):
            let transaction = try checkVerified(verification)
            await transaction.finish()
            isProUnlocked = true
        case .userCancelled:
            break
        case .pending:
            break
        default:
            break
        }
    }

    @MainActor
    public func restore() async throws {
        try await AppStore.sync()
        await refreshEntitlements()
    }

    @MainActor
    public func refreshEntitlements() async {
        for await result in Transaction.currentEntitlements {
            if case .verified(let t) = result, t.productID == productId { isProUnlocked = true; return }
        }
        isProUnlocked = false
    }

    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified(_, let error): throw error
        case .verified(let safe): return safe
        }
    }
}

