import Foundation

public enum Entitlement: String, Codable { case pro }

public struct PaywallState: Codable, Equatable {
    public var isProUnlocked: Bool
}

public protocol StoreProviding: Sendable {
    func isEntitled(_ e: Entitlement) -> Bool
    func purchasePro() async throws
    func restore() async throws
}

public final class DummyStore: StoreProviding {
    private var state = PaywallState(isProUnlocked: true) // 默认解锁，方便开发期
    public init() {}
    public func isEntitled(_ e: Entitlement) -> Bool { state.isProUnlocked }
    public func purchasePro() async throws { state.isProUnlocked = true }
    public func restore() async throws { state.isProUnlocked = true }
}

