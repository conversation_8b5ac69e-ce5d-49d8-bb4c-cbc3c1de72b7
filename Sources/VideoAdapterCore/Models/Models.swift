import Foundation
import CoreGraphics

public struct MediaAsset: Sendable, Hashable, Codable {
    public let id: String
    public let url: URL
    public let duration: Double
    public let dimension: CGSize
    public let fileSize: Int64
}

public enum Platform: String, Codable, Sendable {
    case douyin, xiaohongshu, moments, weibo, bilibili
}

public enum QualityPolicy: String, Codable, Sendable { case quality, balanced, size }

public enum Container: String, Codable, Sendable { case mp4, mov }

public struct Preset: Codable, Sendable, Hashable {
    public let id: String
    public let name: String
    public let platform: Platform
    public let policy: QualityPolicy
    public let width: Int
    public let height: Int
    public let bitrate: Int // kbps
    public let fps: Int?
    public let container: Container
}

public enum JobStatus: String, Codable, Sendable { case pending, running, success, failed, cancelled }

public struct Job: Codable, Sendable, Hashable {
    public let id: String
    public let asset: MediaAsset
    public let presetId: String
    public var status: JobStatus
    public var progress: Double
}

