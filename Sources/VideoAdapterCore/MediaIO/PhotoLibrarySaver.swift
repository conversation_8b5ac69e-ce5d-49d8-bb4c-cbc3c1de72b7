import Foundation
import Photos

public enum PhotoSaveError: Error { case notAuthorized, failed }

public struct PhotoLibrarySaver {
    public init() {}
    public func saveVideo(at url: URL, albumTitle: String? = nil) async throws {
        let status = PHPhotoLibrary.authorizationStatus(for: .readWrite)
        if status == .notDetermined {
            _ = await PHPhotoLibrary.requestAuthorization(for: .readWrite)
        }
        guard PHPhotoLibrary.authorizationStatus(for: .readWrite) == .authorized || PHPhotoLibrary.authorizationStatus(for: .readWrite) == .limited else {
            throw PhotoSaveError.notAuthorized
        }
        try await PHPhotoLibrary.shared().performChanges {
            PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: url)
        }
    }
}

