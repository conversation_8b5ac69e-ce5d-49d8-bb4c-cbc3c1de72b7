import Foundation

public enum CacheManager {
    /// 清理临时目录下以 compressed_ 开头的文件
    @discardableResult
    public static func cleanTemp() -> Int {
        let tmp = URL(fileURLWithPath: NSTemporaryDirectory())
        let fm = FileManager.default
        guard let items = try? fm.contentsOfDirectory(at: tmp, includingPropertiesForKeys: nil) else { return 0 }
        var count = 0
        for u in items where u.lastPathComponent.hasPrefix("compressed_") {
            try? fm.removeItem(at: u)
            count += 1
        }
        return count
    }
}

