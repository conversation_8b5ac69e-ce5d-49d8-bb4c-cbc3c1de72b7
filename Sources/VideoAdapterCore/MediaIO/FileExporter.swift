import Foundation

public enum FileExportError: Error { case failed }

public struct FileExporter {
    public init() {}
    public func moveToDocuments(tempURL: URL, fileName: String? = nil) throws -> URL {
        let docs = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let name = fileName ?? ("compressed_" + UUID().uuidString + ".mp4")
        let dst = docs.appendingPathComponent(name)
        if FileManager.default.fileExists(atPath: dst.path) { try FileManager.default.removeItem(at: dst) }
        try FileManager.default.moveItem(at: tempURL, to: dst)
        return dst
    }
}

