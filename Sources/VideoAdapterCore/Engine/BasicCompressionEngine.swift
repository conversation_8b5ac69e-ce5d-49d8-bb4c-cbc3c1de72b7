import Foundation
import AVFoundation
import CoreImage

/// 基础压缩引擎：使用 AVAssetReader/Writer 与 VideoToolbox(H.264) 进行转码
public final class BasicCompressionEngine: CompressionEngineProviding {
    public init() {}

    public func compress(asset: MediaAsset, preset: Preset, progress: @escaping (Double) -> Void) async throws -> URL {
        let srcURL = asset.url
        let avAsset = AVAsset(url: srcURL)
        guard let videoTrack = try await avAsset.loadTracks(withMediaType: .video).first else {
            throw CompressionError.unsupportedFormat
        }
        let audioTrack = try? await avAsset.loadTracks(withMediaType: .audio).first

        // 输出临时文件
        let tempDir = URL(fileURLWithPath: NSTemporaryDirectory())
        let outURL = tempDir.appendingPathComponent("compressed_\(UUID().uuidString).mp4")
        if FileManager.default.fileExists(atPath: outURL.path) { try? FileManager.default.removeItem(at: outURL) }

        // Reader
        let reader = try AVAssetReader(asset: avAsset)
        // Video composition: 按预设尺寸渲染（等比缩放居中）
        let targetSize = CGSize(width: preset.width, height: preset.height)
        let composition = try await Self.makeVideoComposition(asset: avAsset, videoTrack: videoTrack, targetSize: targetSize)
        let videoOutput = AVAssetReaderVideoCompositionOutput(videoTracks: [videoTrack], videoSettings: [
            kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_420YpCbCr8BiPlanarFullRange
        ])
        videoOutput.videoComposition = composition
        videoOutput.alwaysCopiesSampleData = false
        guard reader.canAdd(videoOutput) else { throw CompressionError.unknown("Cannot add video output") }
        reader.add(videoOutput)

        var audioOutput: AVAssetReaderTrackOutput?
        if let audioTrack = audioTrack {
            let ao = AVAssetReaderTrackOutput(track: audioTrack, outputSettings: [AVFormatIDKey: kAudioFormatLinearPCM])
            ao.alwaysCopiesSampleData = false
            if reader.canAdd(ao) { reader.add(ao); audioOutput = ao }
        }

        // Writer
        let writer = try AVAssetWriter(outputURL: outURL, fileType: .mp4)
        // Video input settings（H.264）
        let videoSettings: [String: Any] = [
            AVVideoCodecKey: AVVideoCodecType.h264,
            AVVideoWidthKey: preset.width,
            AVVideoHeightKey: preset.height,
            AVVideoCompressionPropertiesKey: [
                AVVideoAverageBitRateKey: preset.bitrate * 1000,
                AVVideoProfileLevelKey: AVVideoProfileLevelH264HighAutoLevel,
                AVVideoMaxKeyFrameIntervalKey: 30
            ]
        ]
        let videoInput = AVAssetWriterInput(mediaType: .video, outputSettings: videoSettings)
        videoInput.expectsMediaDataInRealTime = false
        // 保留朝向
        do { let tx = try await videoTrack.load(.preferredTransform); videoInput.transform = tx } catch {}
        guard writer.canAdd(videoInput) else { throw CompressionError.unknown("Cannot add video input") }
        writer.add(videoInput)

        var audioInput: AVAssetWriterInput?
        if audioTrack != nil {
            let ai = AVAssetWriterInput(mediaType: .audio, outputSettings: [
                AVFormatIDKey: kAudioFormatMPEG4AAC,
                AVNumberOfChannelsKey: 2,
                AVSampleRateKey: 44100,
                AVEncoderBitRateKey: 128_000
            ])
            ai.expectsMediaDataInRealTime = false
            if writer.canAdd(ai) { writer.add(ai); audioInput = ai }
        }

        // 开始读写
        guard reader.startReading() else { throw CompressionError.ioError }
        guard writer.startWriting() else { throw CompressionError.ioError }
        writer.startSession(atSourceTime: .zero)

        let totalDuration = try await avAsset.load(.duration)
        let totalSeconds = CMTimeGetSeconds(totalDuration)
        progress(0)

        let group = DispatchGroup()
        let queue = DispatchQueue(label: "basic.engine.writer")

        // Video pipeline
        group.enter()
        videoInput.requestMediaDataWhenReady(on: queue) {
            while videoInput.isReadyForMoreMediaData {
                if Task.isCancelled { videoInput.markAsFinished(); group.leave(); return }
                if let sample = videoOutput.copyNextSampleBuffer() {
                    let time = CMSampleBufferGetPresentationTimeStamp(sample)
                    if totalSeconds.isFinite, totalSeconds > 0 {
                        let p = min(0.95, CMTimeGetSeconds(time) / totalSeconds)
                        progress(p)
                    }
                    _ = videoInput.append(sample)
                } else {
                    videoInput.markAsFinished()
                    group.leave()
                    break
                }
            }
        }

        // Audio pipeline
        if let audioOutput = audioOutput, let audioInput = audioInput {
            group.enter()
            audioInput.requestMediaDataWhenReady(on: queue) {
                while audioInput.isReadyForMoreMediaData {
                    if Task.isCancelled { audioInput.markAsFinished(); group.leave(); return }
                    if let sample = audioOutput.copyNextSampleBuffer() {
                        _ = audioInput.append(sample)
                    } else {
                        audioInput.markAsFinished()
                        group.leave()
                        break
                    }
                }
            }
        }

        group.wait()
        await withCheckedContinuation { (cont: CheckedContinuation<Void, Never>) in
            writer.finishWriting {
                cont.resume()
            }
        }

        switch writer.status {
        case .completed:
            progress(1.0)
            return outURL
        case .cancelled:
            throw CompressionError.cancelled
        default:
            throw CompressionError.ioError
        }
    }

    private static func makeVideoComposition(asset: AVAsset, videoTrack: AVAssetTrack, targetSize: CGSize) async throws -> AVVideoComposition {
        let composition = AVMutableVideoComposition()
        composition.renderSize = targetSize
        composition.frameDuration = CMTimeMake(value: 1, timescale: 30)

        let instruction = AVMutableVideoCompositionInstruction()
        instruction.timeRange = CMTimeRange(start: .zero, duration: try await asset.load(.duration))

        let layerInstruction = AVMutableVideoCompositionLayerInstruction(assetTrack: videoTrack)
        let naturalSize = try await videoTrack.load(.naturalSize)
        let preferredTransform = try await videoTrack.load(.preferredTransform)
        let rect = CGRect(origin: .zero, size: naturalSize).applying(preferredTransform)
        let absSize = CGSize(width: abs(rect.width), height: abs(rect.height))
        let scale = min(targetSize.width / absSize.width, targetSize.height / absSize.height)
        let tx = CGAffineTransform(scaleX: scale, y: scale)
            .translatedBy(x: (targetSize.width/scale - absSize.width)/2,
                          y: (targetSize.height/scale - absSize.height)/2)
            .concatenating(preferredTransform)
        layerInstruction.setTransform(tx, at: .zero)

        instruction.layerInstructions = [layerInstruction]
        composition.instructions = [instruction]
        return composition
    }
}

