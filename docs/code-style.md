# Swift 代码规范与命名约定（项目内标准）

## 1. 总则
- 目标：可读、可测、稳定演进；单人维护优先
- 风格：Swift API Design Guidelines + 本项目约束
- 语言：注释中文为主，专业术语保留英文

## 2. 工程与目录结构
```
App/
  Application/ (App, Scene, Coordinator)
  Features/
    Compression/
    Presets/
    Queue/
    ShareExtension/
    Intents/
  Core/
    CompressionEngine/
    MediaIO/
    Exporter/
    Models/
    Store/
  Resources/
  Tests/
```

## 3. 命名规则
- 类型/协议：PascalCase；函数/变量：camelCase；常量使用 camelCase
- 异步函数以动词开头：compressAsset, exportVideo
- 协议命名以可能力词结尾：…Providing, …Handling
- 枚举 case 使用小写：.pending, .running
- 文件名与类型名一致；扩展以 Type+Feature.swift 命名

## 4. 代码风格
- 每文件不超过 400 行；类型职责单一
- 提前返回避免深层嵌套
- 明确访问控制：默认 internal；对外 API 使用 public；实现细节用 private
- 使用 struct 优先；引用语义需共享可变状态时使用 class

## 5. 错误与日志
- 自定义 Error：CompressionError、IOError 等；包含用户可读描述
- 日志：开发态使用 os.Logger；发布态降级到必要信息
- 不吞异常：捕获即分类并上报给 ViewModel

## 6. 并发与异步
- 使用 async/await；避免裸露回调
- 需要取消的任务使用 Task & Task.cancel()
- CPU 密集型任务使用 Task.detached 或后台队列
- UI 更新在主线程进行（@MainActor）

## 7. 依赖注入
- 通过协议与构造函数注入 Engine、Store 等依赖
- 单例仅限无状态或系统级对象（如 Logger）

## 8. 可测试性
- 核心逻辑（Engine、Presets、Queue）100% 覆盖率优先
- ViewModel 使用可注入的假实现进行测试
- 隔离 IO：为文件/相册操作提供接口，测试时替换为内存实现

## 9. 文档与注释
- 公开 API 使用 /// 文档注释；关键算法块给出简短原理注释
- 所有 IAP、隐私相关逻辑块添加“合规注释”

## 10. 提交与分支
- 提交信息格式：
  - feat: 新功能
  - fix: 修复
  - refactor: 重构
  - perf: 性能
  - docs: 文档
  - test: 测试
  - chore: 杂项
- 分支：main（发布）/ develop（开发）/ feature/*

## 11. 本地化
- 所有对外字符串通过 Localizable.strings 管理
- 使用 String Catalogs（iOS 17+）优先

## 12. 第三方依赖
- 默认无第三方；如必须：SPM 引入，记录版本与来源；避免二进制闭源库

## 13. 安全与隐私
- 不上传媒体；导出前可移除元数据
- IAP 收据校验使用 StoreKit 2，尽量本地校验，避免远端依赖

## 14. UI 规范（代码层）
- SwiftUI 优先（若采用）：状态使用 ObservableObject/State/StateObject
- UIKit 路径：使用自动布局，避免魔法数；统一间距与字号常量

## 15. 版本与兼容
- 最低 iOS 16；使用 @available 做特性降级

