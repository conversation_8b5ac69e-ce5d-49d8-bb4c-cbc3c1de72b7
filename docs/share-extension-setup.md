# Share Extension 目标创建与接线步骤（详尽）

以下步骤在Xcode中完成，将模板代码迁移并跑通一次压缩：

1) 创建目标
- Xcode -> File -> New -> Target… -> iOS -> Share Extension（或 Action Extension）
- Product Name: VideoCompressorShare（可自定义）
- Language: Swift；Embed in Application: 勾选当前App

2) Capabilities
- 在主App与Extension Targets里：添加 App Groups（建议：group.com.your.bundle.video）
- （可选）Photos权限：在主AppInfo.plist添加 NSPhotoLibraryAddUsageDescription 与 NSPhotoLibraryUsageDescription

3) Info.plist 配置
- 参考 App/Extensions/ShareExtensionTemplate/Info.plist.txt 的 NSExtension 配置
- 若使用Storyboard为空，确保移除 MainStoryboard 或置空字符串

4) 复制代码
- 将 App/Extensions/ShareExtensionTemplate/ShareViewController.swift 复制到新建的Extension目录
- 若使用模块名不同，请在 Info.plist 的 NSExtensionPrincipalClass 替换为新模块名

5) 依赖与链接
- 在主工程的 Package Dependencies 中添加本地包（VideoAdapterCore），并在 Extension 的 Target Dependencies 中勾选 VideoAdapterCore

6) 运行与调试
- 选择 Scheme 为新建的 Share Extension
- Run 时Xcode会要求选择一个宿主App（如“照片”或“文件”）；选择后进入分享面板
- 选取一个视频 -> Share Extension弹出 -> 自动执行压缩（模板默认取第一个预设）

7) 可选增强
- 在Share Extension界面添加预设选择器（简化为最近预设）
- 通过App Group读写最近预设ID（UserDefaults(suiteName: "group.…")）
- 将任务提交回宿主App的后台队列（在扩展内只做入队）

故障排查
- 若提示无法加载 VideoAdapterCore：检查Extension的 Build Settings 中 Framework Search Paths 与 Package 依赖是否勾选
- 若无权限访问URL：确认 hasItemConformingToTypeIdentifier 与 UTType.movie 是否匹配

