# 项目架构设计文档（模板/首版）

> 项目：视频压缩与平台适配器（iOS）
> 目标：一键将本地视频压缩为各平台最佳体积与尺寸，支持批量、离线、后台处理与分享扩展；单人开发、低运营、ASO驱动。

## 1. 约束与目标
- 单人开发/低维护：零后端或尽量无后端；所有处理离线完成
- 隐私优先：不上传媒体；仅本地处理与导出
- 快速达成可售MVP：6–8周首版
- 变现：买断（主应用）+ 预设/滤镜/批量包内购（IAP），无订阅（后续可选）
- 平台支持：iOS 16+（支持BackgroundTasks/Share Extension）

## 2. 技术选型
- 媒体处理：AVFoundation（AVAssetReader/Writer）、VideoToolbox（硬件编码）、CoreImage（滤镜/水印可选）
- UI架构：MVVM + Coordinator
- 并发：Swift Concurrency（async/await），必要处用OperationQueue
- 存储：FileManager + App Sandbox；CoreData/SQLite 非MVP必需
- 扩展：Share Extension（快速压缩后分享）、App Intents/Shortcuts（快捷指令）
- 支付：StoreKit 2（买断/IAP）
- 后台：BGProcessingTask，后台压缩队列

## 3. 模块划分与职责
- CompressionEngine（核心编码/转码）
  - 负责读取、转码、复用音视频轨道；控制码率、分辨率、帧率、关键帧间隔
  - 提供进度、取消、错误上报
- Presets（平台预设库）
  - 针对抖音/快手/小红书/朋友圈/微博/B站等，维护目标分辨率、码率、容器、纵横比、最大大小等
  - 支持按“质量/体积优先”策略切换
- MediaIO（输入/输出）
  - 从照片库/文件获取资产；导出到相册/文件；处理权限与存储空间检查
- ProcessingQueue（任务队列）
  - 批量任务编排、重试策略、任务持久化（MVP可内存，后续落地至磁盘）
- Exporter（封装与导出）
  - 处理封装格式（mp4/mov），元数据与可选水印
- ShareExtension（分享扩展）
  - 系统分享面板中一键压缩并回传
- AppIntents（快捷指令）
  - 无UI批处理入口 + 自动化
- Paywall/Store（购买）
  - 买断解锁/功能包解锁、收据校验（本地）

## 4. 数据流与边界
- View 层仅绑定 ViewModel 的可观察状态；禁止直接触达 Engine
- ViewModel 聚合用例：选择预设 -> 创建 Job -> 入队 -> 监听进度 -> 更新UI
- Engine 不依赖 UI/框架，纯 Swift，可单测；通过协议暴露进度/取消
- Presets 纯数据/纯函数；可通过 JSON 定义 + 版本化

## 5. 关键数据模型（草案）
- MediaAsset{id, url, duration, dimension, fileSize}
- Preset{id, name, targetWidth, targetHeight, targetBitrate, maxFileSizeMB?, fps?, profile, platformTags[]}
- Job{id, assets[], presetId, status(pending/running/success/failed), progress(0-1), createdAt}

## 6. 性能策略
- 首选硬件编码（VideoToolbox）；按设备能力降级
- 自适应码率：根据源素材复杂度调整目标码率（后续版本）
- 流水线：解码->滤镜->编码尽可能在同队列串联避免跨队列拷贝
- 内存控制：分段写入、避免整段载入；低内存时降级策略（降低并发、提示）

## 7. 隐私与合规
- 全流程离线；不收集媒体内容或元数据
- 默认不读取定位等敏感EXIF；导出时可选清除元数据
- 隐私政策在本地文档/上架页明确声明

## 8. 错误处理与可恢复性
- 分类：权限/空间不足/不支持的编码/任务被取消/导出失败
- 标准错误域与用户友好提示；失败任务可重试
- 崩溃安全：中间文件自动清理；写入失败回滚

## 9. 本地化与ASO要点
- 语言：中文（首发）+ 英文；后续日/韩可加
- 关键词聚焦：“视频压缩/尺寸裁剪/清晰度/平台预设/一键”
- 截图叙事：1步压缩、平台预设、批量队列、后台处理、隐私离线

## 10. 扩展性路线（概要）
- v1：单视频/多视频压缩，核心平台预设，Share Extension
- v1.1：后台队列、快捷指令、更多平台预设
- v1.2：简单滤镜/水印、清晰度增强（可IAP）
- v1.3：自定义预设导入/导出、自动目标大小

## 11. 依赖管理与第三方
- 首选系统框架；无第三方为默认策略
- 如需第三方（例如轻量UI组件），需评估许可证、维护成本

## 12. 风险与缓解
- 不同平台压制规范更新：预设库版本化 + 在线检查（后续）
- 某些设备编码速率慢：队列并发控制 + 显示预计耗时
- 大文件空间不足：预检查剩余空间 + 建议导出到文件App

## 13. 附：模块关系示意
- UI(View) -> ViewModel -> UseCases -> CompressionEngine/ProcessingQueue -> Exporter -> MediaIO
- ShareExtension/AppIntents -> UseCases -> 同上

