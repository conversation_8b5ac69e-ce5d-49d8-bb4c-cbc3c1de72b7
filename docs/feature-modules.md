# 功能模块开发清单（视频压缩与平台适配器）

## V1（MVP，6–8周可售）
- 导入与权限
  - 从相册选择（单选/多选），从“文件App”导入
  - 权限引导与错误提示
- 预设与压缩
  - 预设列表（平台 + 质量策略：清晰/均衡/体积）
  - 单视频压缩，显示预计尺寸/时长
  - 批量压缩队列（顺序执行、可取消/重试）
  - 导出到相册/文件/分享
- 分享扩展（Share Extension）
  - 其他App中选中视频 -> 选择预设 -> 压缩 -> 回传
- 基础设置
  - 清理缓存、默认预设、是否移除元数据
- 变现
  - 买断解锁：批量、高清预设
  - IAP：高级预设包（如4K->平台最佳、特定社媒清晰度增强）

## V1.1（增强）
- 后台处理（BGProcessingTask）
- 快捷指令（App Intents）
- 更多平台预设与设备自适应

## V1.2（可选增值）
- 简单滤镜/水印（右下角品牌水印可关闭：IAP）
- 自动目标文件大小（基于两阶段估算）

---

## 模块边界与接口契约

### CompressionEngine
- 输入：MediaAsset + Preset
- 输出：导出URL + 元数据（最终尺寸/码率/耗时）
- 接口（示意）：
```
protocol CompressionEngineProviding {
  func compress(asset: MediaAsset, preset: Preset, progress: @escaping (Double)->Void) async throws -> URL
}
```

### Presets
- 数据源：内置 JSON + 版本号
- 查询：按平台/质量策略获取；导出具体编码参数

### ProcessingQueue
- 功能：入队、取消、重试；串行或受控并发
- 事件：onProgress(jobId, value)、onStatusChanged

### MediaIO
- 读取：PHAsset/URL -> MediaAsset
- 写入：URL -> 相册/文件；空间检查

### Exporter
- 容器与元数据处理；可选水印

### Store/Paywall
- 解锁位：isPro, unlockedPresetPacks[]
- StoreKit 2 交易与收据校验

---

## 数据模型（草案）
```
struct MediaAsset { let id: String; let url: URL; let duration: Double; let dimension: CGSize; let fileSize: Int64 }
struct Preset { let id: String; let name: String; let platform: Platform; let policy: QualityPolicy; let width: Int; let height: Int; let bitrate: Int; let fps: Int?; let container: Container }
struct Job { let id: String; let asset: MediaAsset; let presetId: String; var status: JobStatus; var progress: Double }
```

---

## 里程碑验收标准
- 10分钟内完成首个1分钟1080p视频压缩与导出
- 批量3个视频串行完成，进度正确、可取消、失败可重试
- Share Extension 在相册与常见App内可用
- “离线/隐私”声明与设置项可见
- 买断/IAP 流程可在沙盒环境完成

---

## 后续版本候选池
- H265/HEVC 与 HDR 保留/降级策略
- 画面稳像与降噪
- 文案模板与社媒直传（深度链接）
- 设备温控策略（长任务时降频/暂停提示）

