# UI/UX 设计指导原则（视频压缩与平台适配器）

## 1. 用户画像
- 场景1：发社媒的普通用户，追求“一步到位”
- 场景2：小体量创作者，需要批量与平台预设
- 共同偏好：极简流程、默认即最佳、离线与隐私安全

## 2. 设计准则
- 单主流程：选择视频 -> 选择平台预设 -> 压缩 -> 导出
- 预设优先：默认显示常用平台与推荐质量；高级参数折叠
- 批量友好：多选后统一预设，队列化显示进度
- 可撤销与可重做：压缩任务可取消/重试
- 离线与隐私提示：首屏强调“不上传，仅本地处理”

## 3. 关键界面
- 首页：导入（相册/文件）+ 最近使用的预设
- 预设页：平台分组 + 质量策略（清晰/均衡/体积优先）
- 队列页：任务卡片（缩略图、目标规格、进度、预计时间、重试/取消）
- 导出页：导出到相册/文件/分享；导出后复制路径/打开位置

## 4. 交互细节
- 压缩前检查：剩余空间不足/权限未授权 -> 明确引导
- 长任务：显示估算时间，支持后台继续（提示保活条件）
- 错误处理：失败卡片给出可理解原因与立即重试按钮
- 新手引导：首启三步动画；可在设置中重看

## 5. 文案与本地化
- 语气：简洁、确定性强；少技术术语
- 核心卖点文案：
  - 一步压缩，平台直传
  - 全程离线，隐私安全
  - 批量队列，后台处理
- 多语言：中/英首发；截图与元数据统一本地化关键词

## 6. 无障碍（Accessibility）
- 字体自适应；对比度满足 WCAG AA
- VoiceOver 标签与顺序正确
- 重要控件可通过键盘/外设触达
- 动画可降级/关闭

## 7. 空状态与边缘情况
- 无媒体权限：展示原因与一键授权
- 队列为空：提供导入快捷入口与示例
- 空间不足：一键清理缓存并提示导出到“文件App”
- 不支持的格式：给出可支持列表与建议

## 8. 截图与ASO映射
- 截图1：选择预设 -> 一键压缩（主价值）
- 截图2：平台预设矩阵（抖音/小红书/朋友圈/微博/B站）
- 截图3：批量队列与后台处理
- 截图4：离线隐私
- 截图5：导出与分享扩展

## 9. 设计系统（简要）
- 颜色：浅色优先，强调色用于进度与主按钮
- 间距：8pt 基准；栅格统一
- 组件：按钮（主/次）、卡片、进度条、空状态组件、Toast

