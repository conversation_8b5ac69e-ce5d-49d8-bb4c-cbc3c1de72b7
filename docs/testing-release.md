# 测试与发布流程（视频压缩与平台适配器）

## 1. 测试策略
- 单元测试：CompressionEngine、Presets、ProcessingQueue 逻辑100%优先
- 集成测试：单视频压缩 -> 导出；多视频队列 -> 取消/重试
- 手工测试：真实相册素材、极端边界（4K/HDR/超长、空间不足、权限拒绝）

## 2. 测试数据集（建议自建）
- 720p/1080p/4K，30/60fps，时长10s/60s/5min
- 不同编码（H264/H265）、不同比特率
- 竖屏/横屏，不同纵横比

## 3. 用例清单（示例）
- 成功：单视频1080p -> 平台“体积优先”预设 -> 导出到相册
- 失败：空间不足 -> 明确提示并引导清理
- 取消：批量第2个任务中途取消 -> 队列停止并可继续
- 权限：首次拒绝相册 -> 设置页引导授权
- 扩展：在照片App中通过分享扩展压缩并回传

## 4. 性能基线
- iPhone 12/13/14 约1分钟1080p视频压缩用时≤1.2x 实时
- 温度监测：连续压缩≥10分钟不致明显降频（观察帧率与用时）

## 5. 崩溃与日志
- 使用 os.Logger 分类记录：engine/io/store/ui
- 线上可选接入 Xcode Organizer 崩溃收集（无第三方）

## 6. 隐私与合规检查
- 不采集媒体内容/不出网；App Tracking Transparency 不集成
- 隐私政策与数据使用说明与行为一致
- 上架问卷：仅本地处理、无第三方 SDK

## 7. App Store 提交流程（最小化）
- 元数据：
  - 名称（含核心关键词但不过度堆叠）
  - 副标题：一步压缩，平台直传
  - 关键词：视频压缩, 尺寸裁剪, 平台预设, 离线
  - 描述：前三行突出卖点与离线隐私
- 截图：见 UI/UX 指南第8节
- 定价：买断（首发价位建议$6.99-9.99）+ IAP 预设包
- 审核注意：说明“仅本地处理，不上传用户数据”

## 8. ASO 最小策略
- 选择“照片与视频”分类；若强调工具性可辅助选“效率”
- 本地化关键词：中/英；后续可加繁中/日/韩
- 定期A/B：副标题与首图文案（每月一次）

## 9. 发布节奏
- TestFlight 内测：10-20位亲友 + 目标用户（创作者）
- 首发：v1.0 基线功能稳定；两周内发 v1.0.1 修复包
- 后续：每月一次小迭代（预设库/稳定性/性能）

## 10. 回归清单（上线前一日）
- 首次启动与引导
- 导入/权限
- 预设选择与压缩
- 队列/取消/重试
- 导出相册/文件/分享
- Share Extension 流程
- IAP 购买/恢复

## 11. 版本标注与变更日志
- 语义化版本：MAJOR.MINOR.PATCH
- CHANGELOG.md：用户可感知改动优先，技术性改动简述

