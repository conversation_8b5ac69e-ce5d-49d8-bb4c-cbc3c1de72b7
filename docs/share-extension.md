# Share Extension 设计（最小可行）

## 目标
- 在系统分享面板中对单/多视频进行一键压缩，并回传/保存

## 流程（最小）
1) 接收 NSExtensionItem（视频URL/PHAsset）
2) 解析为 MediaAsset 列表
3) 读取用户最近使用的预设（或默认预设）
4) 使用 BasicCompressionEngine 顺序压缩
5) 导出：保存到相册或 openInPlace 回传
6) 通知宿主App（可选：UserDefaults/App Group）

## 技术要点
- Extension 与 App 通信：App Group + UserDefaults/文件共享
- 资源访问：若为文件URL，需开启 NSExtensionFileProvider 权限；PHAsset 需请求权限
- 性能：串行压缩，展示进度（扩展UI有限，尽量简化）

## 后续增强
- 允许选择预设（在扩展UI中）
- 后台继续（提交到宿主App的队列）

