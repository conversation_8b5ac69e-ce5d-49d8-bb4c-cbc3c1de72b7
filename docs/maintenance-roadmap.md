# 长期维护与迭代计划（框架）

## 1. 版本策略
- 语义化版本：MAJOR.MINOR.PATCH
- 发布节奏：
  - 月更：MINOR（预设库更新、性能/稳定）
  - 双月：功能增量（后台/快捷指令/新预设包）
  - 随时：PATCH（热修复）

## 2. 变更日志与配置管理
- CHANGELOG.md：对用户可见的改动置顶
- 预设库 presets.json 版本化（含平台/分辨率/码率）
- 为预设添加 `schemaVersion` 与 `updatedAt`

## 3. 崩溃与反馈收集
- Xcode Organizer 收集崩溃符号化
- 设置页提供反馈入口（邮件模板：包含机型/系统/日志片段）
- 重要错误码与上下文写入本地日志（用户可手动导出）

## 4. 质量基线与技术债
- 性能基线：同测试文档；每次 MINOR 前回归
- 技术债登记：/docs/debt.md；每两次 MINOR 迭代清理一轮

## 5. 预设库更新机制
- v1：随版本内置更新
- v2（可选）：轻量在线检查JSON（不含用户数据；可关闭）
- 更新策略：向后兼容，旧任务引用旧预设快照

## 6. 优先级规则（例）
- P0：崩溃/数据破坏/导出失败
- P1：兼容性问题/大范围性能退化
- P2：小范围UI问题/文案/本地化
- P3：新功能/优化

## 7. 路线图（示例）
- v1.0：MVP（导入/预设/压缩/导出/扩展）
- v1.1：后台队列、快捷指令、更多平台预设
- v1.2：水印/滤镜、目标大小推算（IAP）
- v1.3：自定义预设、导入/导出预设
- v2.0：在线预设更新、更多编码器策略

## 8. 运营最小化
- 无后端/零运维为默认
- FAQ/帮助内置，减少客服沟通
- 通过应用内“分享给朋友解锁一次高级功能”进行轻传播

## 9. 备份与数据
- 用户数据仅为导出结果与设置；支持iCloud备份（系统级）
- 缓存可一键清理；压缩中断自动清理临时文件

## 10. 法务与合规跟踪
- 关注各平台视频规范更新（分辨率/码率/封装）
- App Store 政策变化：IAP、隐私表单

