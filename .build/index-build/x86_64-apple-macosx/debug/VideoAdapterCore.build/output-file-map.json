{"": {"swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/master.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/BasicCompressionEngine.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/BasicCompressionEngine.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/BasicCompressionEngine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/BasicCompressionEngine.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CompressionEngine.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CompressionEngine.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CompressionEngine~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CompressionEngine.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Exporter.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Exporter.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Exporter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Exporter.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CacheManager.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CacheManager.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CacheManager~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CacheManager.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DiskSpace.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DiskSpace.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DiskSpace~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DiskSpace.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/FileExporter.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/FileExporter.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/FileExporter~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/FileExporter.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PhotoLibrarySaver.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PhotoLibrarySaver.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PhotoLibrarySaver~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PhotoLibrarySaver.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Models.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Models.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Models~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Models.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PresetProvider.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PresetProvider.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PresetProvider~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PresetProvider.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/ProcessingQueue.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/ProcessingQueue.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/ProcessingQueue~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/ProcessingQueue.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Paywall.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Paywall.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Paywall~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Paywall.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Store.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Store.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Store~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Store.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/StoreKitStore.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/StoreKitStore.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/StoreKitStore~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/StoreKitStore.swiftdeps"}, "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/resource_bundle_accessor.d", "object": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/resource_bundle_accessor.swiftdeps"}}