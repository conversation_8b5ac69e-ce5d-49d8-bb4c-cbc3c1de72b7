{"builtTestProducts": [{"binaryPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.xctest/Contents/MacOS/VideoAdapterCorePackageTests", "packagePath": "/Users/<USER>/gitlab1/tansuo", "productName": "VideoAdapterCorePackageTests"}], "copyCommands": {"/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/Info.plist": {"inputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Info.plist"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/Info.plist"}]}, "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/presets.json": {"inputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Resources/presets.json"}], "outputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/presets.json"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.VideoAdapterCore-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources", "importPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources"}], "isLibrary": true, "moduleName": "VideoAdapterCore", "moduleOutputPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule", "objects": ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/BasicCompressionEngine.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CompressionEngine.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Exporter.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/CacheManager.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DiskSpace.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/FileExporter.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PhotoLibrarySaver.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Models.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/PresetProvider.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/ProcessingQueue.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Paywall.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Store.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/StoreKitStore.swift.o", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/VideoAdapterCore-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "tansuo"], "outputFileMapPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build", "wholeModuleOptimization": false}, "C.VideoAdapterCorePackageTests-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources", "importPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.derived/runner.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources"}], "isLibrary": true, "moduleName": "VideoAdapterCorePackageTests", "moduleOutputPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCorePackageTests.swiftmodule", "objects": ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/runner.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/VideoAdapterCorePackageTests-Swift.h", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "tansuo"], "outputFileMapPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCorePackageTests.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.derived/runner.swift"], "tempsPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build", "wholeModuleOptimization": false}, "C.VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources", "importPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/VideoAdapterCoreTests.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources"}], "isLibrary": true, "moduleName": "VideoAdapterCoreTests", "moduleOutputPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule", "objects": ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/VideoAdapterCoreTests.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "tansuo"], "outputFileMapPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/VideoAdapterCoreTests.swift"], "tempsPath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"VideoAdapterCore": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "VideoAdapterCore", "-package-name", "tansuo", "-incremental", "-c", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift", "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/VideoAdapterCore-Swift.h", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "tansuo", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "VideoAdapterCorePackageTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "VideoAdapterCorePackageTests", "-package-name", "tansuo", "-incremental", "-c", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.derived/runner.swift", "-I", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx10.13", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/VideoAdapterCorePackageTests-Swift.h", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "tansuo", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "VideoAdapterCoreTests": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "VideoAdapterCoreTests", "-package-name", "tansuo", "-incremental", "-c", "/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/VideoAdapterCoreTests.swift", "-I", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx14.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-Xfrontend", "-enable-cross-import-overlays", "-j12", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "tansuo", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"VideoAdapterCore": [], "VideoAdapterCorePackageTests": ["VideoAdapterCoreTests", "VideoAdapterCore"], "VideoAdapterCoreTests": ["VideoAdapterCore"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources"}, "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.derived/runner.swift"}], "outputFilePath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources"}, "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/VideoAdapterCoreTests.swift"}], "outputFilePath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources"}, "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}