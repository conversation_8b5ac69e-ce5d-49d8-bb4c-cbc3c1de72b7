client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PackageStructure": ["<PackageStructure>"]
  "VideoAdapterCore-x86_64-apple-macosx15.0-debug.module": ["<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module>"]
  "VideoAdapterCorePackageTests-x86_64-apple-macosx15.0-debug.module": ["<VideoAdapterCorePackageTests-x86_64-apple-macosx15.0-debug.module>"]
  "VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module": ["<VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module>"]
  "main": ["<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module>"]
  "test": ["<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module>","<VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources"]
    description: "Write auxiliary file /Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources"

  "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.derived/runner.swift"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources"

  "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/VideoAdapterCoreTests.swift"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources"]
    description: "Write auxiliary file /Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources"

  "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/Info.plist":
    tool: copy-tool
    inputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Info.plist"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/Info.plist"]
    description: "Copying /Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/Info.plist"

  "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/presets.json":
    tool: copy-tool
    inputs: ["/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Resources/presets.json"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/presets.json"]
    description: "Copying /Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Resources/presets.json"

  "/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule"]
    outputs: ["<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module>"]

  "<VideoAdapterCorePackageTests-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCorePackageTests.swiftmodule"]
    outputs: ["<VideoAdapterCorePackageTests-x86_64-apple-macosx15.0-debug.module>"]

  "<VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule"]
    outputs: ["<VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module>"]

  "C.VideoAdapterCore-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/BasicCompressionEngine.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Engine/CompressionEngine.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Exporter/Exporter.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/CacheManager.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/DiskSpace.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/FileExporter.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/MediaIO/PhotoLibrarySaver.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Models/Models.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Presets/PresetProvider.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Queue/ProcessingQueue.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Paywall.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/Store.swift","/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/Store/StoreKitStore.swift","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule"]
    description: "Compiling Swift Module 'VideoAdapterCore' (14 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","VideoAdapterCore","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule","-output-file-map","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/sources","-I","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j12","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore.build/VideoAdapterCore-Swift.h","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","tansuo"]

  "C.VideoAdapterCorePackageTests-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.derived/runner.swift","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCorePackageTests.swiftmodule"]
    description: "Compiling Swift Module 'VideoAdapterCorePackageTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","VideoAdapterCorePackageTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCorePackageTests.swiftmodule","-output-file-map","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/sources","-I","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx10.13","-enable-batch-mode","-index-store-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j12","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCorePackageTests.build/VideoAdapterCorePackageTests-Swift.h","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","tansuo"]

  "C.VideoAdapterCoreTests-x86_64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/VideoAdapterCoreTests.swift","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCore.swiftmodule","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources"]
    outputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule"]
    description: "Compiling Swift Module 'VideoAdapterCoreTests' (1 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","VideoAdapterCoreTests","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules/VideoAdapterCoreTests.swiftmodule","-output-file-map","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/output-file-map.json","-parse-as-library","-incremental","@/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCoreTests.build/sources","-I","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/Modules","-target","x86_64-apple-macosx14.0","-enable-batch-mode","-index-store-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/index/store","-Onone","-enable-testing","-Xfrontend","-enable-cross-import-overlays","-j12","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/ModuleCache","-parseable-output","-parse-as-library","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","tansuo"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/gitlab1/tansuo/Sources/VideoAdapterCore/","/Users/<USER>/gitlab1/tansuo/Tests/VideoAdapterCoreTests/","/Users/<USER>/gitlab1/tansuo/Package.swift","/Users/<USER>/gitlab1/tansuo/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

  "VideoAdapterCore-x86_64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/presets.json","/Users/<USER>/gitlab1/tansuo/.build/index-build/x86_64-apple-macosx/debug/VideoAdapterCore_VideoAdapterCore.bundle/Info.plist"]
    outputs: ["<VideoAdapterCore-x86_64-apple-macosx15.0-debug.module-resources>"]

